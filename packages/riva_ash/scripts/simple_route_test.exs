#!/usr/bin/env elixir

# Simple Route Testing Script
# This script enumerates all routes and checks for basic issues

IO.puts("🔍 Starting Simple Route Enumeration...")

# Load the application
Application.ensure_all_started(:riva_ash)

try do
  # Get all routes from the router
  routes = RivaAshWeb.Router.__routes__()
  
  IO.puts("Found #{length(routes)} routes in total")
  IO.puts("=" |> String.duplicate(50))
  
  # Group routes by type
  public_routes = Enum.filter(routes, fn route ->
    not String.starts_with?(route.path, "/api") and
    not String.starts_with?(route.path, "/admin") and
    not Enum.member?(route.pipe_through, :require_authenticated_user)
  end)
  
  auth_routes = Enum.filter(routes, fn route ->
    Enum.member?(route.pipe_through, :require_authenticated_user)
  end)
  
  api_routes = Enum.filter(routes, fn route ->
    String.starts_with?(route.path, "/api") or String.starts_with?(route.path, "/graphql")
  end)
  
  admin_routes = Enum.filter(routes, fn route ->
    String.starts_with?(route.path, "/admin")
  end)
  
  IO.puts("📊 Route Summary:")
  IO.puts("  Public routes: #{length(public_routes)}")
  IO.puts("  Authenticated routes: #{length(auth_routes)}")
  IO.puts("  API routes: #{length(api_routes)}")
  IO.puts("  Admin routes: #{length(admin_routes)}")
  
  IO.puts("\n🔍 Checking for potential issues...")
  
  # Check for routes with parameters
  param_routes = Enum.filter(routes, fn route ->
    String.contains?(route.path, ":")
  end)
  
  IO.puts("  Routes with parameters: #{length(param_routes)}")
  
  # Check for duplicate paths
  paths = Enum.map(routes, & &1.path)
  duplicate_paths = paths -- Enum.uniq(paths)
  
  if length(duplicate_paths) > 0 do
    IO.puts("  ⚠️  Found duplicate paths: #{inspect(duplicate_paths)}")
  else
    IO.puts("  ✅ No duplicate paths found")
  end
  
  # List all routes for inspection
  IO.puts("\n📋 All Routes:")
  
  Enum.each(routes, fn route ->
    status = cond do
      String.contains?(route.path, ":") -> "📝"
      String.starts_with?(route.path, "/api") -> "🔌"
      String.starts_with?(route.path, "/admin") -> "🔐"
      Enum.member?(route.pipe_through, :require_authenticated_user) -> "🔒"
      true -> "🌐"
    end
    
    IO.puts("  #{status} #{route.verb} #{route.path} -> #{route.plug}")
  end)
  
  # Check for potential problematic routes
  IO.puts("\n⚠️  Potential Issues to Check:")
  
  # Routes with many parameters
  complex_routes = Enum.filter(routes, fn route ->
    (route.path |> String.graphemes() |> Enum.count(& &1 == ":")) > 1
  end)
  
  if length(complex_routes) > 0 do
    IO.puts("  Routes with multiple parameters:")
    Enum.each(complex_routes, fn route ->
      IO.puts("    - #{route.verb} #{route.path}")
    end)
  end
  
  # Very long paths
  long_routes = Enum.filter(routes, fn route ->
    String.length(route.path) > 50
  end)
  
  if length(long_routes) > 0 do
    IO.puts("  Very long route paths:")
    Enum.each(long_routes, fn route ->
      IO.puts("    - #{route.path}")
    end)
  end
  
  # Routes that might conflict
  catch_all_routes = Enum.filter(routes, fn route ->
    String.contains?(route.path, "*")
  end)
  
  if length(catch_all_routes) > 0 do
    IO.puts("  Catch-all routes (check ordering):")
    Enum.each(catch_all_routes, fn route ->
      IO.puts("    - #{route.verb} #{route.path}")
    end)
  end
  
  IO.puts("\n✅ Route enumeration complete!")
  IO.puts("💡 To test these routes with actual HTTP requests, start the server with 'mix phx.server'")
  IO.puts("   and run the live route testing script.")
  
rescue
  error ->
    IO.puts("❌ Error during route enumeration: #{inspect(error)}")
    IO.puts("Make sure the application is properly configured and compiled.")
end
