#!/usr/bin/env elixir

# LiveView Route Testing Script
# This script tests LiveView routes by making HTTP requests to a running server

Mix.install([
  {:httpoison, "~> 2.0"},
  {:jason, "~> 1.4"}
])

defmodule LiveViewRouteTester do
  @moduledoc """
  Tests LiveView routes by making actual HTTP requests to a running Phoenix server.
  """
  
  @base_url "http://localhost:4000"
  
  def run(base_url \\ @base_url) do
    IO.puts("🔍 Testing LiveView routes on: #{base_url}")
    IO.puts("=" |> String.duplicate(50))
    
    # Check if server is running
    case check_server_health(base_url) do
      :ok -> 
        IO.puts("✅ Server is responding")
        test_liveview_routes(base_url)
      :error -> 
        IO.puts("❌ Server is not responding.")
        IO.puts("💡 Start the server with: mix phx.server")
        IO.puts("   Then run this script again.")
        System.halt(1)
    end
  end
  
  defp check_server_health(base_url) do
    case HTTPoison.get("#{base_url}/health", [], timeout: 5_000) do
      {:ok, %HTTPoison.Response{status_code: status}} when status in 200..299 ->
        :ok
      {:ok, %HTTPoison.Response{status_code: 404}} ->
        # Health endpoint might not exist, try root
        case HTTPoison.get(base_url, [], timeout: 5_000) do
          {:ok, %HTTPoison.Response{}} -> :ok
          _ -> :error
        end
      _ -> :error
    end
  end
  
  defp test_liveview_routes(base_url) do
    # LiveView routes without parameters (should be accessible)
    routes_without_params = [
      "/dashboard",
      "/businesses",
      "/businesses/new", 
      "/employees",
      "/clients",
      "/clients/new",
      "/items",
      "/items/new",
      "/item-holds",
      "/item-holds/new",
      "/item-positions", 
      "/item-positions/new",
      "/item-schedules",
      "/item-schedules/new",
      "/item-types",
      "/item-types/new",
      "/layouts",
      "/layouts/new",
      "/payments",
      "/payments/new",
      "/plots",
      "/plots/new",
      "/pricings",
      "/pricings/new",
      "/recurring-reservation-instances",
      "/recurring-reservation-instances/new",
      "/reservations",
      "/reservations/new",
      "/sections",
      "/sections/new",
      "/users",
      "/users/new",
      "/tokens",
      "/tokens/new",
      "/recurring-reservations",
      "/recurring-reservations/new",
      "/availability-exceptions",
      "/availability-exceptions/new",
      "/404",
      "/access-denied"
    ]
    
    IO.puts("\n🧪 Testing #{length(routes_without_params)} LiveView routes without parameters...")
    
    results = %{success: 0, redirects: 0, errors: 0, auth_required: 0}
    
    results = Enum.reduce(routes_without_params, results, fn route, acc ->
      case test_route(base_url, route) do
        :success -> %{acc | success: acc.success + 1}
        :redirect -> %{acc | redirects: acc.redirects + 1}
        :auth_required -> %{acc | auth_required: acc.auth_required + 1}
        :error -> %{acc | errors: acc.errors + 1}
      end
    end)
    
    print_results(results, length(routes_without_params))
    
    # Test some routes with sample IDs
    test_routes_with_params(base_url)
  end
  
  defp test_route(base_url, path) do
    url = "#{base_url}#{path}"
    
    try do
      case HTTPoison.get(url, [], timeout: 10_000, recv_timeout: 10_000) do
        {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
          if String.contains?(body, "LiveView") or String.contains?(body, "phx-") do
            IO.puts("  ✅ #{path} -> 200 (LiveView loaded)")
            :success
          else
            IO.puts("  ✅ #{path} -> 200 (page loaded)")
            :success
          end
        
        {:ok, %HTTPoison.Response{status_code: 302, headers: headers}} ->
          location = get_redirect_location(headers)
          IO.puts("  ↗️  #{path} -> 302 (redirect to #{location})")
          :redirect
        
        {:ok, %HTTPoison.Response{status_code: 401}} ->
          IO.puts("  🔒 #{path} -> 401 (authentication required)")
          :auth_required
        
        {:ok, %HTTPoison.Response{status_code: 403}} ->
          IO.puts("  🚫 #{path} -> 403 (forbidden)")
          :auth_required
        
        {:ok, %HTTPoison.Response{status_code: 404}} ->
          IO.puts("  ❓ #{path} -> 404 (not found)")
          :error
        
        {:ok, %HTTPoison.Response{status_code: 500, body: body}} ->
          IO.puts("  ❌ #{path} -> 500 (SERVER ERROR)")
          IO.puts("     Error: #{String.slice(body, 0, 100)}...")
          :error
        
        {:ok, %HTTPoison.Response{status_code: status}} ->
          IO.puts("  ⚠️  #{path} -> #{status} (unexpected)")
          :error
        
        {:error, %HTTPoison.Error{reason: reason}} ->
          IO.puts("  💥 #{path} -> ERROR: #{inspect(reason)}")
          :error
      end
      
    rescue
      error ->
        IO.puts("  💥 #{path} -> EXCEPTION: #{inspect(error)}")
        :error
    end
  end
  
  defp test_routes_with_params(base_url) do
    IO.puts("\n🧪 Testing LiveView routes with sample parameters...")
    
    # Test with some sample UUIDs (these will likely return 404 or redirect, but shouldn't crash)
    sample_id = Ecto.UUID.generate()
    
    routes_with_params = [
      "/businesses/#{sample_id}/edit",
      "/clients/#{sample_id}/edit",
      "/items/#{sample_id}/edit",
      "/users/#{sample_id}/edit"
    ]
    
    Enum.each(routes_with_params, fn route ->
      case test_route(base_url, route) do
        :error -> 
          IO.puts("    ⚠️  Route #{route} had an error - this might indicate a problem")
        _ -> 
          # Any other response (200, 302, 404) is acceptable for routes with fake IDs
          :ok
      end
    end)
  end
  
  defp get_redirect_location(headers) do
    case Enum.find(headers, fn {key, _value} -> String.downcase(key) == "location" end) do
      {_key, location} -> location
      nil -> "unknown"
    end
  end
  
  defp print_results(results, total) do
    IO.puts("\n📊 LiveView Route Test Results:")
    IO.puts("  Total routes tested: #{total}")
    IO.puts("  ✅ Successful: #{results.success}")
    IO.puts("  ↗️  Redirects: #{results.redirects}")
    IO.puts("  🔒 Auth required: #{results.auth_required}")
    IO.puts("  ❌ Errors: #{results.errors}")
    
    cond do
      results.errors > 0 ->
        IO.puts("\n🚨 Found #{results.errors} errors that need attention!")
        IO.puts("💡 Check the server logs for more details on the errors.")
        
      results.auth_required > 0 ->
        IO.puts("\n🔒 #{results.auth_required} routes require authentication.")
        IO.puts("💡 This is expected for protected routes.")
        
      true ->
        IO.puts("\n🎉 All LiveView routes are responding correctly!")
    end
  end
end

# Parse command line arguments
{opts, args, _} = OptionParser.parse(System.argv(), switches: [url: :string])

base_url = opts[:url] || List.first(args) || "http://localhost:4000"

# Run the LiveView route test
LiveViewRouteTester.run(base_url)
