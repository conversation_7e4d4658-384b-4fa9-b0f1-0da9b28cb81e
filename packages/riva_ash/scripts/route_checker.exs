#!/usr/bin/env elixir

# Route Checker Script
# This script enumerates and tests all routes in the Phoenix application
# to identify potential crashes and errors.

Mix.install([
  {:phoenix, "~> 1.7"},
  {:plug, "~> 1.14"},
  {:jason, "~> 1.4"}
])

defmodule RouteChecker do
  @moduledoc """
  Comprehensive route checking utility for Phoenix applications.
  """

  def run do
    IO.puts("🔍 Starting Route Enumeration and Testing...")
    IO.puts("=" |> String.duplicate(50))
    
    # Load the application
    load_application()
    
    # Get all routes
    routes = get_all_routes()
    
    IO.puts("Found #{length(routes)} total routes")
    
    # Categorize routes
    categorized = categorize_routes(routes)
    print_route_summary(categorized)
    
    # Test routes systematically
    test_all_routes(categorized)
    
    IO.puts("\n✅ Route checking complete!")
  end

  defp load_application do
    # Ensure the application is loaded
    Application.ensure_all_started(:riva_ash)
  rescue
    error ->
      IO.puts("⚠️  Could not start application: #{inspect(error)}")
      IO.puts("Make sure you're running this from the project root")
  end

  defp get_all_routes do
    try do
      RivaAshWeb.Router.__routes__()
    rescue
      error ->
        IO.puts("❌ Could not get routes: #{inspect(error)}")
        []
    end
  end

  defp categorize_routes(routes) do
    %{
      public: Enum.filter(routes, &is_public_route?/1),
      authenticated: Enum.filter(routes, &is_authenticated_route?/1),
      api: Enum.filter(routes, &is_api_route?/1),
      admin: Enum.filter(routes, &is_admin_route?/1),
      live_view: Enum.filter(routes, &is_live_view_route?/1),
      static: Enum.filter(routes, &is_static_route?/1)
    }
  end

  defp is_public_route?(%{path: path, pipe_through: pipes}) do
    not String.starts_with?(path, "/api") and
    not String.starts_with?(path, "/admin") and
    not Enum.member?(pipes, :require_authenticated_user)
  end

  defp is_authenticated_route?(%{pipe_through: pipes}) do
    Enum.member?(pipes, :require_authenticated_user)
  end

  defp is_api_route?(%{path: path}) do
    String.starts_with?(path, "/api") or 
    String.starts_with?(path, "/graphql") or
    String.starts_with?(path, "/docs")
  end

  defp is_admin_route?(%{path: path}) do
    String.starts_with?(path, "/admin")
  end

  defp is_live_view_route?(%{plug: plug}) when is_atom(plug) do
    plug_name = to_string(plug)
    String.contains?(plug_name, "Live")
  end
  defp is_live_view_route?(_), do: false

  defp is_static_route?(%{path: path}) do
    static_extensions = [".css", ".js", ".png", ".jpg", ".ico", ".svg", ".woff"]
    Enum.any?(static_extensions, &String.ends_with?(path, &1))
  end

  defp print_route_summary(categorized) do
    IO.puts("\n📊 Route Summary:")
    IO.puts("  Public routes: #{length(categorized.public)}")
    IO.puts("  Authenticated routes: #{length(categorized.authenticated)}")
    IO.puts("  API routes: #{length(categorized.api)}")
    IO.puts("  Admin routes: #{length(categorized.admin)}")
    IO.puts("  LiveView routes: #{length(categorized.live_view)}")
    IO.puts("  Static routes: #{length(categorized.static)}")
  end

  defp test_all_routes(categorized) do
    IO.puts("\n🧪 Testing Routes...")
    
    # Test each category
    test_route_category("Public", categorized.public)
    test_route_category("API", categorized.api)
    test_route_category("Admin", categorized.admin)
    test_route_category("LiveView", categorized.live_view)
    
    # Test some specific problematic patterns
    test_parameter_routes()
    test_edge_cases()
  end

  defp test_route_category(category_name, routes) do
    IO.puts("\n--- Testing #{category_name} Routes (#{length(routes)}) ---")
    
    results = %{success: 0, errors: 0, skipped: 0}
    
    results = Enum.reduce(routes, results, fn route, acc ->
      case test_single_route(route) do
        :success -> %{acc | success: acc.success + 1}
        :error -> %{acc | errors: acc.errors + 1}
        :skipped -> %{acc | skipped: acc.skipped + 1}
      end
    end)
    
    IO.puts("  Results: ✅ #{results.success} | ❌ #{results.errors} | ⏭️  #{results.skipped}")
  end

  defp test_single_route(%{path: path, verb: verb} = route) do
    # Skip routes with parameters for basic testing
    if String.contains?(path, ":") do
      IO.puts("  ⏭️  #{verb} #{path} (has parameters)")
      :skipped
    else
      try do
        # Here we would make actual HTTP requests if we had a running server
        # For now, we'll do basic validation
        validate_route_structure(route)
        IO.puts("  ✅ #{verb} #{path}")
        :success
      rescue
        error ->
          IO.puts("  ❌ #{verb} #{path} - #{inspect(error)}")
          :error
      end
    end
  end

  defp validate_route_structure(%{path: path, verb: verb, plug: plug}) do
    # Basic validations
    unless is_binary(path) and String.starts_with?(path, "/") do
      raise "Invalid path: #{inspect(path)}"
    end
    
    unless verb in [:get, :post, :put, :patch, :delete, :head, :options] do
      raise "Invalid HTTP verb: #{inspect(verb)}"
    end
    
    unless is_atom(plug) do
      raise "Invalid plug: #{inspect(plug)}"
    end
    
    # Check if the plug module exists
    unless Code.ensure_loaded?(plug) do
      raise "Plug module not found: #{inspect(plug)}"
    end
  end

  defp test_parameter_routes do
    IO.puts("\n--- Testing Parameter Route Patterns ---")
    
    # Common parameter patterns that might cause issues
    parameter_patterns = [
      "/businesses/:id",
      "/clients/:id/edit", 
      "/api/booking/availability/:item_id",
      "/admin/*path"
    ]
    
    Enum.each(parameter_patterns, fn pattern ->
      IO.puts("  📝 Pattern: #{pattern}")
      # Here you could test with sample IDs if you had test data
    end)
  end

  defp test_edge_cases do
    IO.puts("\n--- Testing Edge Cases ---")
    
    edge_cases = [
      "/",
      "/404", 
      "/health",
      "/very/long/path/that/might/cause/issues",
      "/path-with-dashes",
      "/path_with_underscores"
    ]
    
    Enum.each(edge_cases, fn path ->
      IO.puts("  🔍 Edge case: #{path}")
    end)
  end
end

# Run the route checker
RouteChecker.run()
