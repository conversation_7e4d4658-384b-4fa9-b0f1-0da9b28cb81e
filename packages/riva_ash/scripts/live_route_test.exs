#!/usr/bin/env elixir

# Live Route Testing Script
# This script makes actual HTTP requests to test routes on a running server

Mix.install([
  {:httpoison, "~> 2.0"},
  {:jason, "~> 1.4"}
])

defmodule LiveRouteTest do
  @moduledoc """
  Tests routes by making actual HTTP requests to a running Phoenix server.
  """
  
  @base_url "http://localhost:4000"
  
  def run(base_url \\ @base_url) do
    IO.puts("🌐 Testing routes on live server: #{base_url}")
    IO.puts("=" |> String.duplicate(50))
    
    # Check if server is running
    case check_server_health(base_url) do
      :ok -> 
        IO.puts("✅ Server is responding")
        test_all_routes(base_url)
      :error -> 
        IO.puts("❌ Server is not responding. Make sure to start it with: mix phx.server")
        System.halt(1)
    end
  end
  
  defp check_server_health(base_url) do
    case HTTPoison.get("#{base_url}/health") do
      {:ok, %HTTPoison.Response{status_code: status}} when status in 200..299 ->
        :ok
      {:ok, %HTTPoison.Response{status_code: 404}} ->
        # Health endpoint might not exist, try root
        case HTTPoison.get(base_url) do
          {:ok, %HTTPoison.Response{}} -> :ok
          _ -> :error
        end
      _ -> :error
    end
  end
  
  defp test_all_routes(base_url) do
    # Define routes to test (without parameters for now)
    routes_to_test = [
      # Public routes
      %{method: :get, path: "/", expected: [200, 302]},
      %{method: :get, path: "/sign-in", expected: [200]},
      %{method: :get, path: "/register", expected: [200]},
      %{method: :get, path: "/health", expected: [200, 404]},
      %{method: :get, path: "/erd", expected: [200, 302]},
      
      # API routes (should work without auth for some)
      %{method: :get, path: "/api/booking/items", expected: [200, 401]},
      %{method: :get, path: "/docs", expected: [200, 404]},
      %{method: :get, path: "/graphql", expected: [200, 400, 405]}, # POST usually required
      
      # Error routes
      %{method: :get, path: "/404", expected: [404, 200]},
      %{method: :get, path: "/access-denied", expected: [200, 302]},
      %{method: :get, path: "/nonexistent-route", expected: [404]},
      
      # Admin routes (should redirect or deny)
      %{method: :get, path: "/admin", expected: [200, 302, 401, 403]},
      
      # Authenticated routes (should redirect to login)
      %{method: :get, path: "/dashboard", expected: [302, 401]},
      %{method: :get, path: "/businesses", expected: [302, 401]},
      %{method: :get, path: "/clients", expected: [302, 401]}
    ]
    
    IO.puts("\n🧪 Testing #{length(routes_to_test)} routes...")
    
    results = %{success: 0, warnings: 0, errors: 0}
    
    results = Enum.reduce(routes_to_test, results, fn route, acc ->
      case test_route(base_url, route) do
        :success -> %{acc | success: acc.success + 1}
        :warning -> %{acc | warnings: acc.warnings + 1}
        :error -> %{acc | errors: acc.errors + 1}
      end
    end)
    
    print_final_results(results)
  end
  
  defp test_route(base_url, %{method: method, path: path, expected: expected_codes}) do
    url = "#{base_url}#{path}"
    
    try do
      response = case method do
        :get -> HTTPoison.get(url, [], timeout: 10_000, recv_timeout: 10_000)
        :post -> HTTPoison.post(url, "", [], timeout: 10_000, recv_timeout: 10_000)
        :put -> HTTPoison.put(url, "", [], timeout: 10_000, recv_timeout: 10_000)
        :patch -> HTTPoison.patch(url, "", [], timeout: 10_000, recv_timeout: 10_000)
        :delete -> HTTPoison.delete(url, [], timeout: 10_000, recv_timeout: 10_000)
      end
      
      case response do
        {:ok, %HTTPoison.Response{status_code: status_code, body: body}} ->
          cond do
            status_code in expected_codes ->
              IO.puts("  ✅ #{String.upcase(to_string(method))} #{path} -> #{status_code}")
              :success
            
            status_code == 500 ->
              IO.puts("  ❌ #{String.upcase(to_string(method))} #{path} -> #{status_code} (SERVER ERROR)")
              IO.puts("     Body: #{String.slice(body, 0, 200)}...")
              :error
            
            true ->
              IO.puts("  ⚠️  #{String.upcase(to_string(method))} #{path} -> #{status_code} (unexpected)")
              :warning
          end
        
        {:error, %HTTPoison.Error{reason: reason}} ->
          IO.puts("  ❌ #{String.upcase(to_string(method))} #{path} -> ERROR: #{inspect(reason)}")
          :error
      end
      
    rescue
      error ->
        IO.puts("  💥 #{String.upcase(to_string(method))} #{path} -> EXCEPTION: #{inspect(error)}")
        :error
    end
  end
  
  defp print_final_results(results) do
    total = results.success + results.warnings + results.errors
    
    IO.puts("\n📊 Final Results:")
    IO.puts("  Total routes tested: #{total}")
    IO.puts("  ✅ Successful: #{results.success}")
    IO.puts("  ⚠️  Warnings: #{results.warnings}")
    IO.puts("  ❌ Errors: #{results.errors}")
    
    cond do
      results.errors > 0 ->
        IO.puts("\n🚨 Found #{results.errors} critical errors that need immediate attention!")
        System.halt(1)
      
      results.warnings > 0 ->
        IO.puts("\n⚠️  Found #{results.warnings} warnings that should be reviewed.")
        
      true ->
        IO.puts("\n🎉 All routes are responding as expected!")
    end
  end
end

# Parse command line arguments
{opts, args, _} = OptionParser.parse(System.argv(), switches: [url: :string])

base_url = opts[:url] || List.first(args) || "http://localhost:4000"

# Run the live route test
LiveRouteTest.run(base_url)
